import h5py
import numpy as np
import pandas as pd
import json
import argparse
import logging
import yaml
from pathlib import Path
from typing import Dict, Tu<PERSON>, Optional
from dataclasses import dataclass, asdict

import matplotlib.pyplot as plt
import seaborn as sns

import umap
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class UMAPConfig:
    """Configuration for UMAP parameters"""
    n_neighbors: int = 15
    min_dist: float = 0.1
    n_components: int = 2
    metric: str = 'cosine'
    random_state: int = 42
    n_jobs: int = -1

@dataclass
class TSNEConfig:
    """Configuration for t-SNE parameters"""
    n_components: int = 2
    perplexity: float = 30.0
    learning_rate: str = 'auto'
    max_iter: int = 1000
    random_state: int = 42
    n_jobs: int = -1
    metric: str = 'cosine'

@dataclass
class VisualizationConfig:
    """Configuration for visualization parameters"""
    max_samples: int = 10000  # Development limit
    use_pca_preprocessing: bool = True
    pca_components: int = 128
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 300
    color_palette: str = 'Dark2'

class HDF5DataLoader:
    """Efficient HDF5 data loader with memory management"""

    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        if not self.file_path.exists():
            raise FileNotFoundError(f"HDF5 file not found: {file_path}")

        self.datasets_info = self._get_datasets_info()
        logger.info(f"Loaded HDF5 file: {file_path}")
        logger.info(f"Available datasets: {list(self.datasets_info.keys())}")

    def _get_datasets_info(self) -> Dict:
        """Get information about available datasets"""
        info = {}
        with h5py.File(self.file_path, 'r') as f:
            for key in f.keys():
                dataset = f[key]
                info[key] = {
                    'shape': dataset.shape,
                    'dtype': dataset.dtype,
                    'size': dataset.size
                }
        return info

    def load_embeddings(self, dataset_name: str = 'embeddings',
                       max_samples: Optional[int] = None,
                       random_seed: int = 42) -> np.ndarray:
        """Load embeddings with optional sampling"""
        with h5py.File(self.file_path, 'r') as f:
            if dataset_name not in f:
                raise KeyError(f"Dataset '{dataset_name}' not found in HDF5 file")

            dataset = f[dataset_name]
            total_samples = dataset.shape[0]

            if max_samples and max_samples < total_samples:
                # Random sampling for development
                np.random.seed(random_seed)
                indices = np.random.choice(total_samples, max_samples, replace=False)
                indices = np.sort(indices)  # Sort for efficient HDF5 access
                embeddings = dataset[indices]
                logger.info(f"Sampled {max_samples} vectors from {total_samples} total vectors")
                return embeddings, indices
            else:
                embeddings = dataset[:]
                logger.info(f"Loaded all {total_samples} vectors")
                return embeddings, np.arange(total_samples)

    def load_metadata(self, dataset_name: str = 'embeddings_metadata',
                     indices: Optional[np.ndarray] = None) -> pd.DataFrame:
        """Load and parse metadata"""
        with h5py.File(self.file_path, 'r') as f:
            if dataset_name not in f:
                logger.warning(f"Metadata dataset '{dataset_name}' not found")
                return pd.DataFrame()

            dataset = f[dataset_name]

            if indices is not None:
                metadata_raw = dataset[indices]
            else:
                metadata_raw = dataset[:]

            # Parse JSON metadata
            metadata_list = []
            for item in metadata_raw:
                if isinstance(item, np.ndarray) and len(item) > 0:
                    json_str = item[0].decode('utf-8') if isinstance(item[0], bytes) else str(item[0])
                    metadata_list.append(json.loads(json_str))
                else:
                    metadata_list.append({})

            df = pd.DataFrame(metadata_list)
            logger.info(f"Parsed metadata with columns: {list(df.columns)}")
            return df

class DimensionalityReducer:
    """Dimensionality reduction with UMAP and t-SNE support"""

    def __init__(self, umap_config: UMAPConfig = None, tsne_config: TSNEConfig = None):
        self.umap_config = umap_config or UMAPConfig()
        self.tsne_config = tsne_config or TSNEConfig()
        self.umap_model = None
        self.tsne_model = None
        self.scaler = None
        self.pca = None

    def preprocess_data(self, embeddings: np.ndarray,
                       use_pca: bool = True,
                       pca_components: int = 50) -> np.ndarray:
        """Preprocess embeddings with optional PCA"""
        logger.info(f"Preprocessing embeddings of shape {embeddings.shape}")

        # Standardize the data
        self.scaler = StandardScaler()
        embeddings_scaled = self.scaler.fit_transform(embeddings)

        if use_pca and embeddings.shape[1] > pca_components:
            logger.info(f"Applying PCA reduction from {embeddings.shape[1]} to {pca_components} dimensions")
            self.pca = PCA(n_components=pca_components, random_state=self.umap_config.random_state)
            embeddings_processed = self.pca.fit_transform(embeddings_scaled)

            # Log explained variance
            explained_var = np.sum(self.pca.explained_variance_ratio_)
            logger.info(f"PCA explained variance ratio: {explained_var:.3f}")
        else:
            embeddings_processed = embeddings_scaled

        return embeddings_processed

    def fit_umap(self, embeddings: np.ndarray) -> np.ndarray:
        """Fit UMAP and return 2D embeddings"""
        logger.info("Fitting UMAP model...")

        self.umap_model = umap.UMAP(
            n_neighbors=self.umap_config.n_neighbors,
            min_dist=self.umap_config.min_dist,
            n_components=self.umap_config.n_components,
            metric=self.umap_config.metric,
            random_state=self.umap_config.random_state,
            n_jobs=self.umap_config.n_jobs,
        )

        umap_embeddings = self.umap_model.fit_transform(embeddings)
        logger.info(f"UMAP completed. Output shape: {umap_embeddings.shape}")

        return umap_embeddings

    def fit_tsne(self, embeddings: np.ndarray) -> np.ndarray:
        """Fit t-SNE and return 2D embeddings"""
        logger.info("Fitting t-SNE model...")

        self.tsne_model = TSNE(
            n_components=self.tsne_config.n_components,
            perplexity=self.tsne_config.perplexity,
            learning_rate=self.tsne_config.learning_rate,
            max_iter=self.tsne_config.max_iter,
            random_state=self.tsne_config.random_state,
            n_jobs=self.tsne_config.n_jobs,
            metric=self.tsne_config.metric,
            verbose=1
        )

        tsne_embeddings = self.tsne_model.fit_transform(embeddings)
        logger.info(f"t-SNE completed. Output shape: {tsne_embeddings.shape}")

        return tsne_embeddings

class Visualizer:
    """Generate various UMAP visualizations with metadata integration"""

    def __init__(self, config: VisualizationConfig):
        self.config = config
        plt.style.use('seaborn-v0_8')
        sns.set_palette(config.color_palette)

    def create_plot(self, umap_embeddings: np.ndarray,
                           metadata_df: pd.DataFrame,
                           output_path: str = None) -> plt.Figure:
        """Create UMAP plot colored by metadata"""
        fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)

        ax.scatter(
            umap_embeddings[:, 0],
            umap_embeddings[:, 1],
            c=metadata_df.point_category_id,
            alpha=0.8,
            s=20,
            cmap=self.config.color_palette
        )

        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_xlabel('')
        ax.set_ylabel('')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if output_path:
            plt.savefig(output_path, dpi=self.config.dpi, bbox_inches='tight')
            logger.info(f"Metadata plot saved to {output_path}")

        return fig




def save_config(config_dict: Dict, output_path: str):
    """Save configuration to YAML file"""
    with open(output_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False)
    logger.info(f"Configuration saved to {output_path}")

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Generate UMAP visualizations from HDF5 embeddings')
    parser.add_argument('--input', '-i', default='data/test-embeddings.hdf5',
                       help='Path to HDF5 file containing embeddings')
    parser.add_argument('--max-samples', '-n', type=int, default=10000,
                       help='Maximum number of samples for development (default: 10000)')
    parser.add_argument('--output-dir', '-o', default='output',
                       help='Output directory for visualizations')
    parser.add_argument('--config', '-c', default=None,
                       help='Path to configuration YAML file')

    args = parser.parse_args()

    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    umap_config = UMAPConfig()
    tsne_config = TSNEConfig()
    viz_config = VisualizationConfig(max_samples=args.max_samples)

    config_dict = {
        'umap': asdict(umap_config),
        'tsne': asdict(tsne_config),
        'visualization': asdict(viz_config)
    }
    save_config(config_dict, str(output_dir / 'config.yaml'))


    logger.info("=== Stage 1: Data Loading ===")

    loader = HDF5DataLoader(args.input)

    embeddings, indices = loader.load_embeddings(
        dataset_name="embeddings",
        max_samples=viz_config.max_samples
    )

    metadata_df = loader.load_metadata(indices=indices)

    logger.info(f"Loaded {embeddings.shape[0]} embeddings with {embeddings.shape[1]} dimensions")
    logger.info(f"Metadata shape: {metadata_df.shape}")

    # Stage 2: Dimensionality Reduction
    logger.info("=== Stage 2: Dimensionality Reduction ===")
    reducer = DimensionalityReducer(umap_config, tsne_config)

    # Preprocess
    embeddings_processed = reducer.preprocess_data(
        embeddings,
        use_pca=viz_config.use_pca_preprocessing,
        pca_components=viz_config.pca_components
    )

    results = {}

    logger.info("Fitting UMAP...")
    umap_embeddings = reducer.fit_umap(embeddings_processed)
    results['umap'] = umap_embeddings

    logger.info("Fitting t-SNE...")
    tsne_embeddings = reducer.fit_tsne(embeddings_processed)
    results['tsne'] = tsne_embeddings

    # Stage 3: Generate visualizations
    logger.info("Generating visualizations...")
    visualizer = Visualizer(viz_config)

    # Generate visualizations for each method
    for method_name, embeddings in results.items():
        logger.info(f"Generating {method_name.upper()} visualizations...")

        visualizer.create_plot(
            embeddings,
            metadata_df,
            output_path=str(output_dir / f'{method_name}_by_point_category_id.png')
            )

if __name__ == "__main__":
    main()