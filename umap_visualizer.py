#!/usr/bin/env python3
"""
UMAP Visualization Script for Large-Scale Vector Datasets

This script generates 2D UMAP visualizations from HDF5-stored vector embeddings
with rich metadata integration and performance optimization.

Author: Augment Agent
"""

import h5py
import numpy as np
import pandas as pd
import json
import argparse
import logging
import yaml
import time
import psutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass, asdict
import warnings

# Visualization libraries
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# ML libraries
import umap
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.utils import resample

# Progress tracking
from tqdm import tqdm

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class UMAPConfig:
    """Configuration for UMAP parameters"""
    n_neighbors: int = 15
    min_dist: float = 0.1
    n_components: int = 2
    metric: str = 'cosine'
    random_state: int = 42
    n_jobs: int = -1

@dataclass
class TSNEConfig:
    """Configuration for t-SNE parameters"""
    n_components: int = 2
    perplexity: float = 30.0
    learning_rate: str = 'auto'
    max_iter: int = 1000
    random_state: int = 42
    n_jobs: int = -1

@dataclass
class VisualizationConfig:
    """Configuration for visualization parameters"""
    max_samples: int = 10000  # Development limit
    use_pca_preprocessing: bool = True
    pca_components: int = 50
    figure_size: Tuple[int, int] = (12, 8)
    dpi: int = 300
    color_palette: str = 'viridis'

class HDF5DataLoader:
    """Efficient HDF5 data loader with memory management"""

    def __init__(self, file_path: str):
        self.file_path = Path(file_path)
        if not self.file_path.exists():
            raise FileNotFoundError(f"HDF5 file not found: {file_path}")

        self.datasets_info = self._get_datasets_info()
        logger.info(f"Loaded HDF5 file: {file_path}")
        logger.info(f"Available datasets: {list(self.datasets_info.keys())}")

    def _get_datasets_info(self) -> Dict:
        """Get information about available datasets"""
        info = {}
        with h5py.File(self.file_path, 'r') as f:
            for key in f.keys():
                dataset = f[key]
                info[key] = {
                    'shape': dataset.shape,
                    'dtype': dataset.dtype,
                    'size': dataset.size
                }
        return info

    def load_embeddings(self, dataset_name: str = 'embeddings',
                       max_samples: Optional[int] = None,
                       random_seed: int = 42) -> np.ndarray:
        """Load embeddings with optional sampling"""
        with h5py.File(self.file_path, 'r') as f:
            if dataset_name not in f:
                raise KeyError(f"Dataset '{dataset_name}' not found in HDF5 file")

            dataset = f[dataset_name]
            total_samples = dataset.shape[0]

            if max_samples and max_samples < total_samples:
                # Random sampling for development
                np.random.seed(random_seed)
                indices = np.random.choice(total_samples, max_samples, replace=False)
                indices = np.sort(indices)  # Sort for efficient HDF5 access
                embeddings = dataset[indices]
                logger.info(f"Sampled {max_samples} vectors from {total_samples} total vectors")
                return embeddings, indices
            else:
                embeddings = dataset[:]
                logger.info(f"Loaded all {total_samples} vectors")
                return embeddings, np.arange(total_samples)

    def load_metadata(self, dataset_name: str = 'embeddings_metadata',
                     indices: Optional[np.ndarray] = None) -> pd.DataFrame:
        """Load and parse metadata"""
        with h5py.File(self.file_path, 'r') as f:
            if dataset_name not in f:
                logger.warning(f"Metadata dataset '{dataset_name}' not found")
                return pd.DataFrame()

            dataset = f[dataset_name]

            if indices is not None:
                metadata_raw = dataset[indices]
            else:
                metadata_raw = dataset[:]

            # Parse JSON metadata
            metadata_list = []
            for item in tqdm(metadata_raw, desc="Parsing metadata"):
                try:
                    if isinstance(item, np.ndarray) and len(item) > 0:
                        json_str = item[0].decode('utf-8') if isinstance(item[0], bytes) else str(item[0])
                        metadata_list.append(json.loads(json_str))
                    else:
                        metadata_list.append({})
                except (json.JSONDecodeError, UnicodeDecodeError, AttributeError) as e:
                    logger.warning(f"Failed to parse metadata item: {e}")
                    metadata_list.append({})

            df = pd.DataFrame(metadata_list)
            logger.info(f"Parsed metadata with columns: {list(df.columns)}")
            return df

class DimensionalityReducer:
    """Dimensionality reduction with UMAP and t-SNE support"""

    def __init__(self, umap_config: UMAPConfig = None, tsne_config: TSNEConfig = None):
        self.umap_config = umap_config or UMAPConfig()
        self.tsne_config = tsne_config or TSNEConfig()
        self.umap_model = None
        self.tsne_model = None
        self.scaler = None
        self.pca = None

    def preprocess_data(self, embeddings: np.ndarray,
                       use_pca: bool = True,
                       pca_components: int = 50) -> np.ndarray:
        """Preprocess embeddings with optional PCA"""
        logger.info(f"Preprocessing embeddings of shape {embeddings.shape}")

        # Standardize the data
        self.scaler = StandardScaler()
        embeddings_scaled = self.scaler.fit_transform(embeddings)

        if use_pca and embeddings.shape[1] > pca_components:
            logger.info(f"Applying PCA reduction from {embeddings.shape[1]} to {pca_components} dimensions")
            self.pca = PCA(n_components=pca_components, random_state=self.umap_config.random_state)
            embeddings_processed = self.pca.fit_transform(embeddings_scaled)

            # Log explained variance
            explained_var = np.sum(self.pca.explained_variance_ratio_)
            logger.info(f"PCA explained variance ratio: {explained_var:.3f}")
        else:
            embeddings_processed = embeddings_scaled

        return embeddings_processed

    def fit_umap(self, embeddings: np.ndarray) -> np.ndarray:
        """Fit UMAP and return 2D embeddings"""
        logger.info("Fitting UMAP model...")

        self.umap_model = umap.UMAP(
            n_neighbors=self.umap_config.n_neighbors,
            min_dist=self.umap_config.min_dist,
            n_components=self.umap_config.n_components,
            metric=self.umap_config.metric,
            random_state=self.umap_config.random_state,
            n_jobs=self.umap_config.n_jobs,
            verbose=True
        )

        umap_embeddings = self.umap_model.fit_transform(embeddings)
        logger.info(f"UMAP completed. Output shape: {umap_embeddings.shape}")

        return umap_embeddings

    def fit_tsne(self, embeddings: np.ndarray) -> np.ndarray:
        """Fit t-SNE and return 2D embeddings"""
        logger.info("Fitting t-SNE model...")

        self.tsne_model = TSNE(
            n_components=self.tsne_config.n_components,
            perplexity=self.tsne_config.perplexity,
            learning_rate=self.tsne_config.learning_rate,
            max_iter=self.tsne_config.max_iter,
            random_state=self.tsne_config.random_state,
            n_jobs=self.tsne_config.n_jobs,
            verbose=1
        )

        tsne_embeddings = self.tsne_model.fit_transform(embeddings)
        logger.info(f"t-SNE completed. Output shape: {tsne_embeddings.shape}")

        return tsne_embeddings

class UMAPVisualizer:
    """Generate various UMAP visualizations with metadata integration"""

    def __init__(self, config: VisualizationConfig):
        self.config = config
        plt.style.use('seaborn-v0_8')
        sns.set_palette(config.color_palette)

    def create_basic_plot(self, umap_embeddings: np.ndarray,
                         output_path: str = None) -> plt.Figure:
        """Create basic UMAP scatter plot"""
        fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)

        scatter = ax.scatter(umap_embeddings[:, 0], umap_embeddings[:, 1],
                           alpha=0.6, s=20, c='steelblue')

        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_xlabel('')
        ax.set_ylabel('')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if output_path:
            plt.savefig(output_path, dpi=self.config.dpi, bbox_inches='tight')
            logger.info(f"Basic plot saved to {output_path}")

        return fig

    def create_metadata_plot(self, umap_embeddings: np.ndarray,
                           metadata_df: pd.DataFrame,
                           color_by: str = None,
                           output_path: str = None) -> plt.Figure:
        """Create UMAP plot colored by metadata"""
        fig, ax = plt.subplots(figsize=self.config.figure_size, dpi=self.config.dpi)

        if color_by and color_by in metadata_df.columns:
            # Handle different data types
            if metadata_df[color_by].dtype == 'object':
                # Categorical data
                unique_vals = metadata_df[color_by].unique()
                colors = plt.cm.Set3(np.linspace(0, 1, len(unique_vals)))

                for i, val in enumerate(unique_vals):
                    mask = metadata_df[color_by] == val
                    if mask.sum() > 0:
                        ax.scatter(umap_embeddings[mask, 0], umap_embeddings[mask, 1],
                                 alpha=0.6, s=20, c=[colors[i]], label=str(val))

                ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
            else:
                # Numerical data
                scatter = ax.scatter(umap_embeddings[:, 0], umap_embeddings[:, 1],
                                   c=metadata_df[color_by], alpha=0.6, s=20,
                                   cmap=self.config.color_palette)
                plt.colorbar(scatter, ax=ax, label=color_by)

        else:
            ax.scatter(umap_embeddings[:, 0], umap_embeddings[:, 1],
                      alpha=0.6, s=20, c='steelblue')

        ax.set_xticks([])
        ax.set_yticks([])
        ax.set_xlabel('')
        ax.set_ylabel('')
        ax.grid(True, alpha=0.3)

        plt.tight_layout()

        if output_path:
            plt.savefig(output_path, dpi=self.config.dpi, bbox_inches='tight')
            logger.info(f"Metadata plot saved to {output_path}")

        return fig

    def create_interactive_plot(self, umap_embeddings: np.ndarray,
                              metadata_df: pd.DataFrame,
                              color_by: str = None,
                              output_path: str = None) -> go.Figure:
        """Create interactive Plotly visualization"""

        # Prepare data for plotting
        plot_df = pd.DataFrame({
            'UMAP_1': umap_embeddings[:, 0],
            'UMAP_2': umap_embeddings[:, 1]
        })

        # Add metadata columns for hover information
        hover_data = []
        for col in metadata_df.columns:
            if col in ['point_id', 'image_id', 'geohash', 'captured_at']:
                plot_df[col] = metadata_df[col].astype(str)
                hover_data.append(col)

        # Add numerical metadata
        for col in ['x', 'y', 'radius']:
            if col in metadata_df.columns:
                plot_df[col] = pd.to_numeric(metadata_df[col], errors='coerce')
                hover_data.append(col)

        # Create the plot
        if color_by and color_by in metadata_df.columns:
            plot_df[color_by] = metadata_df[color_by]

            if metadata_df[color_by].dtype == 'object':
                # Categorical coloring
                fig = px.scatter(plot_df, x='UMAP_1', y='UMAP_2',
                               color=color_by, hover_data=hover_data)
            else:
                # Continuous coloring
                fig = px.scatter(plot_df, x='UMAP_1', y='UMAP_2',
                               color=color_by, hover_data=hover_data,
                               color_continuous_scale='viridis')
        else:
            fig = px.scatter(plot_df, x='UMAP_1', y='UMAP_2', hover_data=hover_data)

        # Customize layout
        fig.update_layout(
            width=800, height=600,
            xaxis_title='',
            yaxis_title='',
            font=dict(size=12),
            xaxis=dict(showticklabels=False),
            yaxis=dict(showticklabels=False)
        )

        fig.update_traces(marker=dict(size=5, opacity=0.7))

        if output_path:
            fig.write_html(output_path)
            logger.info(f"Interactive plot saved to {output_path}")

        return fig

class PerformanceMonitor:
    """Monitor memory usage and performance"""

    def __init__(self):
        self.start_time = time.time()
        self.process = psutil.Process()

    def log_memory_usage(self, stage: str):
        """Log current memory usage"""
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        elapsed = time.time() - self.start_time
        logger.info(f"{stage} - Memory: {memory_mb:.1f} MB, Elapsed: {elapsed:.1f}s")

    def get_memory_usage(self) -> float:
        """Get current memory usage in MB"""
        return self.process.memory_info().rss / 1024 / 1024

def save_config(config_dict: Dict, output_path: str):
    """Save configuration to YAML file"""
    with open(output_path, 'w') as f:
        yaml.dump(config_dict, f, default_flow_style=False)
    logger.info(f"Configuration saved to {output_path}")

def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    logger.info(f"Configuration loaded from {config_path}")
    return config

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Generate UMAP visualizations from HDF5 embeddings')
    parser.add_argument('--input', '-i', default='data/test-embeddings.hdf5',
                       help='Path to HDF5 file containing embeddings')
    parser.add_argument('--dataset', '-d', default='embeddings',
                       help='Dataset name in HDF5 file')
    parser.add_argument('--max-samples', '-n', type=int, default=10000,
                       help='Maximum number of samples for development (default: 10000)')
    parser.add_argument('--output-dir', '-o', default='output',
                       help='Output directory for visualizations')
    parser.add_argument('--config', '-c', default=None,
                       help='Path to configuration YAML file')
    parser.add_argument('--save-config', action='store_true',
                       help='Save current configuration to output directory')
    parser.add_argument('--method', '-m', choices=['umap', 'tsne', 'both'], default='both',
                       help='Dimensionality reduction method (default: both)')
    parser.add_argument('--umap-only', action='store_true',
                       help='Generate only UMAP visualizations')
    parser.add_argument('--tsne-only', action='store_true',
                       help='Generate only t-SNE visualizations')

    args = parser.parse_args()

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    # Determine which methods to use
    use_umap = not args.tsne_only and (args.method in ['umap', 'both'] or args.umap_only)
    use_tsne = not args.umap_only and (args.method in ['tsne', 'both'] or args.tsne_only)

    # Initialize configurations
    if args.config and Path(args.config).exists():
        config_dict = load_config(args.config)
        umap_config = UMAPConfig(**config_dict.get('umap', {}))
        tsne_config = TSNEConfig(**config_dict.get('tsne', {}))
        viz_config = VisualizationConfig(**config_dict.get('visualization', {}))
        viz_config.max_samples = args.max_samples  # Override with command line
    else:
        umap_config = UMAPConfig()
        tsne_config = TSNEConfig()
        viz_config = VisualizationConfig(max_samples=args.max_samples)

    # Save configuration if requested
    if args.save_config:
        config_dict = {
            'umap': asdict(umap_config),
            'tsne': asdict(tsne_config),
            'visualization': asdict(viz_config)
        }
        save_config(config_dict, str(output_dir / 'config.yaml'))

    # Initialize performance monitor
    monitor = PerformanceMonitor()

    try:
        # Stage 1: Load data
        logger.info("=== Stage 1: Data Loading ===")
        monitor.log_memory_usage("Start")

        loader = HDF5DataLoader(args.input)

        embeddings, indices = loader.load_embeddings(
            dataset_name=args.dataset,
            max_samples=viz_config.max_samples
        )

        metadata_df = loader.load_metadata(indices=indices)
        monitor.log_memory_usage("Data Loading Complete")

        logger.info(f"Loaded {embeddings.shape[0]} embeddings with {embeddings.shape[1]} dimensions")
        logger.info(f"Metadata shape: {metadata_df.shape}")

        # Stage 2: Dimensionality Reduction
        logger.info("=== Stage 2: Dimensionality Reduction ===")
        reducer = DimensionalityReducer(umap_config, tsne_config)

        # Preprocess
        embeddings_processed = reducer.preprocess_data(
            embeddings,
            use_pca=viz_config.use_pca_preprocessing,
            pca_components=viz_config.pca_components
        )
        monitor.log_memory_usage("Preprocessing Complete")

        # Fit models based on user selection
        results = {}

        if use_umap:
            logger.info("Fitting UMAP...")
            umap_embeddings = reducer.fit_umap(embeddings_processed)
            results['umap'] = umap_embeddings
            monitor.log_memory_usage("UMAP Complete")

        if use_tsne:
            logger.info("Fitting t-SNE...")
            tsne_embeddings = reducer.fit_tsne(embeddings_processed)
            results['tsne'] = tsne_embeddings
            monitor.log_memory_usage("t-SNE Complete")

        # Stage 3: Generate visualizations
        logger.info("=== Stage 3: Visualization Generation ===")
        visualizer = UMAPVisualizer(viz_config)

        # Find interesting columns for coloring
        interesting_columns = []
        if not metadata_df.empty:
            # Prioritize point_category_id first, then other interesting columns
            priority_columns = ['point_category_id', 'geohash', 'captured_at', 'x', 'y', 'radius']
            for col in priority_columns:
                if col in metadata_df.columns and not metadata_df[col].isna().all():
                    interesting_columns.append(col)

        generated_files = []

        # Generate visualizations for each method
        for method_name, embeddings in results.items():
            logger.info(f"Generating {method_name.upper()} visualizations...")

            # Create basic plot
            basic_fig = visualizer.create_basic_plot(
                embeddings,
                output_path=str(output_dir / f'{method_name}_basic.png')
            )
            plt.close(basic_fig)
            generated_files.append(f'{method_name}_basic.png')

            # Create plots colored by different metadata features
            for col in interesting_columns[:3]:  # Limit to first 3 for development
                try:
                    metadata_fig = visualizer.create_metadata_plot(
                        embeddings, metadata_df, color_by=col,
                        output_path=str(output_dir / f'{method_name}_by_{col}.png')
                    )
                    plt.close(metadata_fig)
                    generated_files.append(f'{method_name}_by_{col}.png')
                except Exception as e:
                    logger.warning(f"Failed to create {method_name} plot for {col}: {e}")

            # Create interactive plot
            try:
                visualizer.create_interactive_plot(
                    embeddings, metadata_df,
                    color_by=interesting_columns[0] if interesting_columns else None,
                    output_path=str(output_dir / f'{method_name}_interactive.html')
                )
                generated_files.append(f'{method_name}_interactive.html')
            except Exception as e:
                logger.warning(f"Failed to create {method_name} interactive plot: {e}")

        monitor.log_memory_usage("Visualization Complete")

        # Save results for future use
        for method_name, embeddings in results.items():
            np.save(output_dir / f'{method_name}_embeddings.npy', embeddings)
        metadata_df.to_pickle(output_dir / 'metadata.pkl')

        # Final performance summary
        total_time = time.time() - monitor.start_time
        final_memory = monitor.get_memory_usage()

        logger.info(f"Results saved to {output_dir}")
        logger.info("All stages completed successfully!")
        logger.info(f"Total processing time: {total_time:.1f} seconds")
        logger.info(f"Peak memory usage: {final_memory:.1f} MB")
        logger.info(f"Methods used: {', '.join(results.keys()).upper()}")
        logger.info(f"Generated visualizations:")
        for file in generated_files:
            logger.info(f"  - {output_dir / file}")

    except Exception as e:
        logger.error(f"Error in processing: {e}")
        raise

if __name__ == "__main__":
    main()